import { http, HttpResponse, delay } from 'msw';
import { resultSuccess, resultError, serverApi } from './_util';

// 模拟用户数据
const mockUsers = [
  {
    username: 'admin',
    password: 'a123456',
    token: 'mock-admin-token-123456',
    userInfo: {
      id: 1,
      username: 'admin',
      nickname: '管理员',
      email: '<EMAIL>',
      phone: '13800138000',
      avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
      remark: '系统管理员',
    },
  },
  {
    username: 'user',
    password: '123456',
    token: 'mock-user-token-123456',
    userInfo: {
      id: 2,
      username: 'user',
      nickname: '普通用户',
      email: '<EMAIL>',
      phone: '13800138001',
      avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
      remark: '普通用户',
    },
  },
];

// 模拟菜单数据
const mockMenus = [
  {
    id: 1,
    path: '/dashboard',
    name: 'Dashboard',
    component: 'dashboard/index',
    meta: {
      title: '仪表盘',
      icon: 'ant-design:dashboard-outlined',
      orderNo: 1,
    },
  },
  {
    id: 2,
    path: '/system',
    name: 'System',
    component: 'system/index',
    meta: {
      title: '系统管理',
      icon: 'ant-design:setting-outlined',
      orderNo: 2,
    },
    children: [
      {
        id: 21,
        path: '/system/user',
        name: 'SystemUser',
        component: 'system/user/index',
        meta: {
          title: '用户管理',
          icon: 'ant-design:user-outlined',
          orderNo: 1,
        },
      },
      {
        id: 22,
        path: '/system/role',
        name: 'SystemRole',
        component: 'system/role/index',
        meta: {
          title: '角色管理',
          icon: 'ant-design:team-outlined',
          orderNo: 2,
        },
      },
    ],
  },
];

// 模拟权限数据
const mockPermissions = [
  'system:user:read',
  'system:user:create',
  'system:user:update',
  'system:user:delete',
  'system:role:read',
  'system:role:create',
  'system:role:update',
  'system:role:delete',
];

export default [
  // 登录接口
  http.post(serverApi('/auth/login'), async ({ request }) => {
    await delay(800);
    
    const body = await request.json() as any;
    const { username, password } = body;

    // 查找用户
    const user = mockUsers.find(u => u.username === username && u.password === password);
    
    if (!user) {
      return HttpResponse.json(
        resultError('用户名或密码错误', { code: 401 }),
        { status: 401 }
      );
    }

    return HttpResponse.json(
      resultSuccess({
        token: user.token,
      })
    );
  }),

  // 获取用户信息
  http.get(serverApi('/account/profile'), async ({ request }) => {
    await delay(300);
    
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');
    
    const user = mockUsers.find(u => u.token === token);
    
    if (!user) {
      return HttpResponse.json(
        resultError('未授权', { code: 401 }),
        { status: 401 }
      );
    }

    return HttpResponse.json(resultSuccess(user.userInfo));
  }),

  // 获取菜单
  http.get(serverApi('/account/menus'), async ({ request }) => {
    await delay(300);
    
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');
    
    const user = mockUsers.find(u => u.token === token);
    
    if (!user) {
      return HttpResponse.json(
        resultError('未授权', { code: 401 }),
        { status: 401 }
      );
    }

    return HttpResponse.json(resultSuccess(mockMenus));
  }),

  // 获取权限
  http.get(serverApi('/account/permissions'), async ({ request }) => {
    await delay(300);
    
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');
    
    const user = mockUsers.find(u => u.token === token);
    
    if (!user) {
      return HttpResponse.json(
        resultError('未授权', { code: 401 }),
        { status: 401 }
      );
    }

    return HttpResponse.json(resultSuccess(mockPermissions));
  }),

  // 登出接口
  http.get(serverApi('/account/logout'), async () => {
    await delay(300);
    return HttpResponse.json(resultSuccess('登出成功'));
  }),

  // 验证码接口（返回空数据，因为我们要去掉验证码）
  http.get(serverApi('/auth/captcha/img'), async () => {
    await delay(200);
    return HttpResponse.json(
      resultSuccess({
        id: 'mock-captcha-id',
        img: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      })
    );
  }),
];
