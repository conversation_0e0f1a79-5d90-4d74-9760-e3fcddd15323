{"name": "vue3-antdv-admin", "type": "module", "version": "2.0.0", "packageManager": "pnpm@10.10.0", "author": {"name": "b<PERSON>qi<PERSON>", "email": "<EMAIL>", "url": "https://github.com/buqiyuan"}, "license": "MIT", "homepage": "https://buqiyuan.gitee.io/vue3-antdv-admin", "repository": {"type": "git", "url": "https://github.com/buqiyuan/vue3-antdv-admin"}, "keywords": ["vue", "ant-design-vue", "vue3", "ts", "tsx", "admin", "typescript"], "engines": {"node": ">=20.19.0", "pnpm": ">=10.0.0"}, "scripts": {"preinstall": "npx only-allow pnpm", "postinstall": "pnpm nx:build", "bootstrap": "pnpm install", "serve": "npm run dev", "dev": "vite dev", "build": "rimraf dist && cross-env NODE_ENV=production vite build", "build:watch": "rimraf dist && cross-env NODE_ENV=production vite build --watch", "build:pkg": "pnpm -r --paralle --filter=\"./packages/*\" run build", "nx:build": "nx run-many -t build --exclude @admin-pkg/components", "nx:build:watch": "nx watch --all -- nx run \\$NX_PROJECT_NAME:build", "preview": "npm run build --watch && vite preview", "preview:dist": "vite preview", "openapi": "npx tsx openapi.config.ts", "clean:cache": "npx rimraf node_modules/.cache/ && npx rimraf node_modules/.vite", "clean:lib": "npx rimraf node_modules packages/*/node_modules", "lint": "eslint", "lint:fix": "eslint --fix", "lint:lint-staged": "lint-staged", "prepare": "husky", "release": "git push && git push origin --tags", "gen:changelog": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && pnpm clean:lib && npm run bootstrap", "test:gzip": "npx http-server dist --cors --gzip -c-1", "test:br": "npx http-server dist --cors --brotli -c-1"}, "dependencies": {"@ant-design/icons-vue": "~7.0.1", "@iconify/vue": "^5.0.0", "@tinymce/tinymce-vue": "^6.1.0", "@vueuse/core": "~13.2.0", "ant-design-vue": "~4.2.6", "axios": "~1.9.0", "crypto-js": "^4.2.0", "dayjs": "~1.11.13", "echarts": "^5.6.0", "file-saver": "~2.0.5", "lodash-es": "~4.17.21", "mitt": "~3.0.1", "nprogress": "~1.0.0-1", "pinia": "~3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "qiniu-js": "^3.4.2", "qs": "~6.14.0", "sortablejs": "~1.15.6", "tinymce": "^7.9.0", "vue": "~3.5.14", "vue-echarts": "^7.0.3", "vue-i18n": "~11.1.4", "vue-router": "~4.5.1", "vue-types": "~6.0.0", "vue-virtual-scroller": "2.0.0-beta.8", "xlsx": "~0.18.5"}, "devDependencies": {"@admin-pkg/components": "workspace:*", "@admin-pkg/vite-plugin-http2-proxy": "workspace:*", "@admin-pkg/vite-plugin-msw": "workspace:*", "@admin-pkg/vite-plugin-tinymce-resource": "workspace:*", "@antfu/eslint-config": "^4.13.2", "@commitlint/cli": "~19.5.0", "@commitlint/config-conventional": "~19.5.0", "@faker-js/faker": "^9.8.0", "@iconify-json/ant-design": "^1.2.5", "@iconify-json/ep": "^1.2.2", "@iconify/json": "^2.2.339", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "~4.17.12", "@types/node": "~22.15.18", "@types/qs": "^6.14.0", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "~8.32.1", "@typescript-eslint/parser": "~8.32.1", "@umijs/openapi": "^1.13.0", "@unocss/eslint-plugin": "^66.1.2", "@vitejs/plugin-vue": "~5.2.4", "@vitejs/plugin-vue-jsx": "~4.2.0", "@vue/tsconfig": "^0.7.0", "commitizen": "~4.3.1", "conventional-changelog-cli": "~4.1.0", "core-js": "^3.42.0", "cross-env": "~7.0.3", "eslint": "~9.27.0", "eslint-plugin-format": "^1.0.1", "husky": "~9.1.7", "less": "~4.3.0", "lint-staged": "~15.2.11", "msw": "^2.4.9", "nx": "^21.1.2", "rimraf": "~5.0.9", "typescript": "~5.8.3", "unocss": "^66.1.2", "unplugin-vue-components": "~28.5.0", "vite": "~6.3.5", "vite-plugin-checker": "~0.9.3", "vite-plugin-mkcert": "^1.17.8", "vite-plugin-svg-icons": "~2.0.1", "vite-plugin-vue-devtools": "^7.7.6", "vue-eslint-parser": "~10.1.3", "vue-tsc": "~2.2.10"}, "__npminstall_done": false, "target": "web", "volta": {"node": "22.14.0", "pnpm": "10.11.0"}, "pnpm": {"overrides": {}, "peerDependencyRules": {"allowedVersions": {}}}}