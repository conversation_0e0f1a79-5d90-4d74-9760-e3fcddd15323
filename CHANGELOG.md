# 1.1.0 (2022-09-02)

### Bug Fixes

- :bug: router navigation bug ([b550a7a](https://github.com/buqiyuan/vite-vue3-admin/commit/b550a7a01b486cfd161ccb8efd7bca9a0ed35627))
- :bug:生成路由出错问题 ([917e3f0](https://github.com/buqiyuan/vite-vue3-admin/commit/917e3f07209851a3df0b7e220e38df4df7612a68))
- :bug:修复某些权限码传参错误 ([47071ef](https://github.com/buqiyuan/vite-vue3-admin/commit/47071efa7beb2e210151405e2a1c7e74d6dff4f1))
- :bug:fix BASE_URL to VITE_BASE_URL ([3d32e82](https://github.com/buqiyuan/vite-vue3-admin/commit/3d32e82b838aea3c8a928989d2aa55c5d125a8ab))
- 面包屑导航和弹窗拖拽后宽度问题 ([ae96559](https://github.com/buqiyuan/vite-vue3-admin/commit/ae96559fb82a87908a40b425701eaa0ee1993aa7))
- 删除一些多余的旧代码 ([9eaa568](https://github.com/buqiyuan/vite-vue3-admin/commit/9eaa568a104e7ab1980c0d2069cbedddb4a4934b))
- add menu type judgment ([144a539](https://github.com/buqiyuan/vite-vue3-admin/commit/144a53942a9229cb5e0286ae3452b644d66621e6))
- **component:** :bug:修复 dynamicTable ts 类型错误 ([58b9275](https://github.com/buqiyuan/vite-vue3-admin/commit/58b9275758625f583aed644923431df72acb9687))
- **components:** [dynamic-table] initial fetchData did not carry the default value ([3aaedcf](https://github.com/buqiyuan/vite-vue3-admin/commit/3aaedcf3c0551477e05dec85fb86d4e5af62b193))
- **components:** [dynamic-table] parameter missing ([1e306d7](https://github.com/buqiyuan/vite-vue3-admin/commit/1e306d77d205cc9fe69fb19d860f5e014e8accc8))
- **dynamic-table:** add onChangeParams param for dataRequest ([f381c79](https://github.com/buqiyuan/vite-vue3-admin/commit/f381c793529b6711994e001d29d1b8e6dc016631))
- fix the aformPropsKeys ([#22](https://github.com/buqiyuan/vite-vue3-admin/issues/22)) ([e1c21be](https://github.com/buqiyuan/vite-vue3-admin/commit/e1c21bea3dddee1fefc9465142f390b0252ebcb4))
- invalid regular expression in safari ([3939f82](https://github.com/buqiyuan/vite-vue3-admin/commit/3939f8229561959cb46982f64c54f4348258a1bd)), closes [#20](https://github.com/buqiyuan/vite-vue3-admin/issues/20)
- isAsyncFunction ([71aca13](https://github.com/buqiyuan/vite-vue3-admin/commit/71aca13b8055cd38c9f49a4d370c935312fa4d6f))
- **pages:** about page link issues ([62c840c](https://github.com/buqiyuan/vite-vue3-admin/commit/62c840ccec3aa4b4237a182d750db933797c1c92))
- **projects:** 修复 tabs-view 下拉菜单溢出 ([a43353d](https://github.com/buqiyuan/vite-vue3-admin/commit/a43353dc89f8395278b6988f7a2dd8c372ff0d7f))
- remove topLevelAwait usage ([6653da6](https://github.com/buqiyuan/vite-vue3-admin/commit/6653da65f7a6d9738a39c955fb4dcb6d4f553235))
- router redirect error when logout ([4073cb6](https://github.com/buqiyuan/vite-vue3-admin/commit/4073cb651b3bf45c16ce5f965fb7dddd3fab3bcd))
- **router:** 第一次进入页面缓存失效问题 ([1b79adc](https://github.com/buqiyuan/vite-vue3-admin/commit/1b79adc072c1c9b6518cc4c05846dfd47c954989))
- **schema-form:** update props issue ([ff1da5e](https://github.com/buqiyuan/vite-vue3-admin/commit/ff1da5e4f9723392f8af6a5286131f8cbf3c2bf8))
- some css style issues ([9db10b0](https://github.com/buqiyuan/vite-vue3-admin/commit/9db10b058c8ba212cf51c94c10d2f37705ff7112))
- some route file path error ([c89b131](https://github.com/buqiyuan/vite-vue3-admin/commit/c89b131d77c0ee8036813a2d377774bb854c652d))
- some ts type issue ([60ea702](https://github.com/buqiyuan/vite-vue3-admin/commit/60ea702d7fe9cc88f85c07cdb0cd6fe5f3c56669))
- svg can not loaded ([6aec46a](https://github.com/buqiyuan/vite-vue3-admin/commit/6aec46a00412a2b02e4090faca6722717a739550))
- **utils:** [is] always false of isPromise ([8479111](https://github.com/buqiyuan/vite-vue3-admin/commit/84791110a87ab48f131c91e73508c2547fff8b25))
- xlsx.js not default export in new versions [#8](https://github.com/buqiyuan/vite-vue3-admin/issues/8) ([a0b0fc8](https://github.com/buqiyuan/vite-vue3-admin/commit/a0b0fc8c78e1cf75e5ed7e48aee03dc7ce364db4))

### Features

- 表格列设置工具栏 ([a934e12](https://github.com/buqiyuan/vite-vue3-admin/commit/a934e123426fab27fcbfbf47181f3355f584974a))
- 当前用户角色权限变更时实时更新权限菜单 ([89918a2](https://github.com/buqiyuan/vite-vue3-admin/commit/89918a2195b119f480f498d420bb29016d557846))
- 服务监控页面 ([0c3d61f](https://github.com/buqiyuan/vite-vue3-admin/commit/0c3d61fbdf4b0ecb1375990c06f5b039bed37085))
- 全局挂载 Reflect 反射对象 ([f6f4675](https://github.com/buqiyuan/vite-vue3-admin/commit/f6f4675fba8e94b8b8bb67eff79d1205e3b06fff))
- 新增按钮权限 ([5538d38](https://github.com/buqiyuan/vite-vue3-admin/commit/5538d387925b7bd53f332643903f9e4cacad0908))
- **components:** [dynamic-table] cell support defaultEditable ([125bb08](https://github.com/buqiyuan/vite-vue3-admin/commit/125bb08ef2563f4dd4f3883da679e739e3f80bad))
- **components:** [dynamic-table] support cell edit ([4411b0e](https://github.com/buqiyuan/vite-vue3-admin/commit/4411b0e49feeb93b3d1034fc038c9a778d7312af))
- **components:** [ProjectSetting] add layout mode ([815b0c2](https://github.com/buqiyuan/vite-vue3-admin/commit/815b0c2cdc063a848b88cfe35519b0755b279282))
- edit-row-table support save loading ([4d0eea6](https://github.com/buqiyuan/vite-vue3-admin/commit/4d0eea6d30d8b09d30cafb6e3bb4b80bb806c675))
- support for nested routes ([9d9e1d8](https://github.com/buqiyuan/vite-vue3-admin/commit/9d9e1d856f01e0164c88a18583957d20ace95654))
- **tools:** :art: add project config drawer ([c1f0de0](https://github.com/buqiyuan/vite-vue3-admin/commit/c1f0de05f25bbbadbcbc9f4a105e8d721b008bbb))
- update basic-form demo ([27f95ec](https://github.com/buqiyuan/vite-vue3-admin/commit/27f95ec4e7c1b57fca5af379165960167aea2e1b))
- **views:** add about page ([0a34802](https://github.com/buqiyuan/vite-vue3-admin/commit/0a34802b7ef2d2727df4b3b769bd4d664ace2bfa))

### Performance Improvements

- **tableColumns:** customRender is easy ([#10](https://github.com/buqiyuan/vite-vue3-admin/issues/10)) ([e069f3c](https://github.com/buqiyuan/vite-vue3-admin/commit/e069f3c164f92e414638cb2f681013c7dc7727a0))
- use vite-plugin-style-import replace unplugin-vue-components [#5](https://github.com/buqiyuan/vite-vue3-admin/issues/5) ([10540eb](https://github.com/buqiyuan/vite-vue3-admin/commit/10540eb1de36f4cd6048f86e5b0363109d571760))

## 1.0.2 (2022-03-15)

### Bug Fixes

- :bug: router navigation bug ([b550a7a](https://github.com/buqiyuan/vite-vue3-admin/commit/b550a7a01b486cfd161ccb8efd7bca9a0ed35627))
- :bug:生成路由出错问题 ([917e3f0](https://github.com/buqiyuan/vite-vue3-admin/commit/917e3f07209851a3df0b7e220e38df4df7612a68))
- :bug:修复某些权限码传参错误 ([47071ef](https://github.com/buqiyuan/vite-vue3-admin/commit/47071efa7beb2e210151405e2a1c7e74d6dff4f1))
- :bug:fix BASE_URL to VITE_BASE_URL ([3d32e82](https://github.com/buqiyuan/vite-vue3-admin/commit/3d32e82b838aea3c8a928989d2aa55c5d125a8ab))
- 面包屑导航和弹窗拖拽后宽度问题 ([ae96559](https://github.com/buqiyuan/vite-vue3-admin/commit/ae96559fb82a87908a40b425701eaa0ee1993aa7))
- 删除一些多余的旧代码 ([9eaa568](https://github.com/buqiyuan/vite-vue3-admin/commit/9eaa568a104e7ab1980c0d2069cbedddb4a4934b))
- **component:** :bug:修复 dynamicTable ts 类型错误 ([58b9275](https://github.com/buqiyuan/vite-vue3-admin/commit/58b9275758625f583aed644923431df72acb9687))
- remove topLevelAwait usage ([6653da6](https://github.com/buqiyuan/vite-vue3-admin/commit/6653da65f7a6d9738a39c955fb4dcb6d4f553235))
- **router:** 第一次进入页面缓存失效问题 ([1b79adc](https://github.com/buqiyuan/vite-vue3-admin/commit/1b79adc072c1c9b6518cc4c05846dfd47c954989))
- some route file path error ([c89b131](https://github.com/buqiyuan/vite-vue3-admin/commit/c89b131d77c0ee8036813a2d377774bb854c652d))
- svg can not loaded ([6aec46a](https://github.com/buqiyuan/vite-vue3-admin/commit/6aec46a00412a2b02e4090faca6722717a739550))
- xlsx.js not default export in new versions [#8](https://github.com/buqiyuan/vite-vue3-admin/issues/8) ([a0b0fc8](https://github.com/buqiyuan/vite-vue3-admin/commit/a0b0fc8c78e1cf75e5ed7e48aee03dc7ce364db4))

### Features

- 表格列设置工具栏 ([a934e12](https://github.com/buqiyuan/vite-vue3-admin/commit/a934e123426fab27fcbfbf47181f3355f584974a))
- 服务监控页面 ([0c3d61f](https://github.com/buqiyuan/vite-vue3-admin/commit/0c3d61fbdf4b0ecb1375990c06f5b039bed37085))
- 全局挂载 Reflect 反射对象 ([f6f4675](https://github.com/buqiyuan/vite-vue3-admin/commit/f6f4675fba8e94b8b8bb67eff79d1205e3b06fff))
- 新增按钮权限 ([5538d38](https://github.com/buqiyuan/vite-vue3-admin/commit/5538d387925b7bd53f332643903f9e4cacad0908))
- **views:** add about page ([0a34802](https://github.com/buqiyuan/vite-vue3-admin/commit/0a34802b7ef2d2727df4b3b769bd4d664ace2bfa))

### Performance Improvements

- **tableColumns:** customRender is easy ([#10](https://github.com/buqiyuan/vite-vue3-admin/issues/10)) ([e069f3c](https://github.com/buqiyuan/vite-vue3-admin/commit/e069f3c164f92e414638cb2f681013c7dc7727a0))
- use vite-plugin-style-import replace unplugin-vue-components [#5](https://github.com/buqiyuan/vite-vue3-admin/issues/5) ([10540eb](https://github.com/buqiyuan/vite-vue3-admin/commit/10540eb1de36f4cd6048f86e5b0363109d571760))
