{"name": "@admin-pkg/vite-plugin-msw", "version": "0.0.9", "type": "module", "description": "", "module": "./dist/browser/index.js", "main": "./dist/browser/index.js", "files": ["dist"], "exports": {".": {"import": "./dist/browser/index.js"}, "./vite": {"import": "./dist/index.js"}}, "scripts": {"dev": "rimraf dist && tsup --watch", "build": "rimraf dist && tsup"}, "keywords": ["msw", "mock", "vite"], "author": {"name": "b<PERSON>qi<PERSON>", "email": "<EMAIL>", "url": "https://github.com/buqiyuan"}, "repository": {"type": "git", "url": "https://github.com/buqiyuan/vue3-antdv-admin/tree/main/packages/vite-plugin-msw", "directory": "packages/vite-plugin-msw"}, "homepage": "https://github.com/buqiyuan/vue3-antdv-admin/tree/main/packages/vite-plugin-msw#readme", "license": "MIT", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "dependencies": {"@mswjs/interceptors": "^0.36.1", "headers-polyfill": "^4.0.3", "strict-event-emitter": "^0.5.1"}, "devDependencies": {"tsup": "^8.3.0"}, "peerDependencies": {"msw": "^2.3.0"}}