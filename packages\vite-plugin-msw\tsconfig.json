{"compilerOptions": {"baseUrl": ".", "target": "esnext", "module": "esnext", "moduleResolution": "node", "outDir": "dist", "declaration": true, "esModuleInterop": true, "resolveJsonModule": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "useUnknownInCatchVariables": false, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["node", "vite/client"]}, "include": ["**/*.ts"]}