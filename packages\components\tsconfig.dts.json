{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "allowJs": true,
    "checkJs": false,
    // Generate d.ts files
    "declaration": true,
    // only output d.ts files
    "emitDeclarationOnly": true,
    "declarationDir": "dist/types",
    "strict": false,
    "noEmit": false,
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "lib": ["ESNext", "DOM"]
  },
  "include": ["./dist/index.es.js"]
}
