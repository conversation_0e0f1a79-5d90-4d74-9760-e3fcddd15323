<svg width="182" height="166" xmlns="http://www.w3.org/2000/svg">
 <!-- Generator: Sketch 54 (76480) - https://sketchapp.com -->
 <title>未命名</title>
 <desc>Created with Sketch.</desc>
 <defs>
  <linearGradient x1="0.00542331488%" y1="50.0074349%" x2="100.011743%" y2="50.0074349%" id="linearGradient-1">
   <stop stop-color="#3668E7" offset="0.3030465%"/>
   <stop stop-color="#44BAE1" offset="100%"/>
  </linearGradient>
  <linearGradient x1="0.0117592657%" y1="50.0044985%" x2="100.006481%" y2="50.0044985%" id="linearGradient-2">
   <stop stop-color="#44BCE1" offset="0%"/>
   <stop stop-color="#44BAE1" offset="36.72%"/>
   <stop stop-color="#43B2E2" offset="54.5%"/>
   <stop stop-color="#41A5E2" offset="68.19%"/>
   <stop stop-color="#3F93E4" offset="79.8%"/>
   <stop stop-color="#3C7BE5" offset="89.99%"/>
   <stop stop-color="#385EE7" offset="99.21%"/>
   <stop stop-color="#385BE7" offset="100%"/>
  </linearGradient>
  <linearGradient x1="0.0148416278%" y1="50.0005568%" x2="100.005996%" y2="50.0005568%" id="linearGradient-3">
   <stop stop-color="#44BCE1" offset="0%"/>
   <stop stop-color="#43B6E1" offset="14.76%"/>
   <stop stop-color="#41A4E2" offset="34.89%"/>
   <stop stop-color="#3E88E4" offset="58.12%"/>
   <stop stop-color="#3960E7" offset="83.49%"/>
   <stop stop-color="#385BE7" offset="86.36%"/>
  </linearGradient>
 </defs>
 <g>
  <title>background</title>
  <rect x="-1" y="-1" width="184" height="168" id="canvas_background" fill="none"/>
 </g>
 <g>
  <title>Layer 1</title>
  <g id="logo3-01的副本" fill-rule="nonzero" stroke="null" transform="rotate(-1 88.74440002441469,82.13372802734358) ">
   <g id="图层_2" stroke="null" transform="rotate(-1 88.74440002441469,82.13372802734358) ">
    <g id="XMLID_5_" stroke="null">
     <g id="编组" stroke="null">
      <path d="m85.59977,58.69808c-18.60016,1.68406 -33.15083,17.00902 -33.15083,35.70211c0,19.80456 16.36951,35.83683 36.51396,35.83683c0,0 74.57221,12.15893 56.93295,-76.69217c10.15802,16.70589 19.42378,33.51283 19.42378,33.51283c0,0 22.64964,38.36292 -2.47087,61.80507c0,0 -14.585,13.40513 -41.55865,13.40513c-7.41261,0 -16.30088,0 -25.36073,0c-47.22107,-8.15086 -51.20191,-49.17461 -51.20191,-49.17461c-3.26018,-52.74481 40.8723,-54.5636 40.8723,-54.5636l0,0.16841z" id="路径" fill="url(#linearGradient-1)" stroke="null" stroke-width="0"/>
      <path d="m145.89585,53.54485c17.63926,88.8511 -56.93295,76.69217 -56.93295,76.69217c20.17877,0 36.51396,-16.03227 36.51396,-35.83683c0,-3.33445 -0.48045,-6.56784 -1.33839,-9.63284c0,0 0,-0.03368 0,-0.03368c-0.37749,-1.91983 -6.0399,-28.3596 -33.52833,-38.63238c-12.52593,-4.68169 -26.35594,-4.34488 -38.91619,0.20209c-8.81963,3.19972 -19.18356,8.75712 -26.83639,18.32259l19.04629,-32.90656c4.35833,-7.51092 10.05506,-14.17981 16.98722,-19.50144c12.11413,-9.22866 31.19473,-16.97534 52.91779,-0.97676c0,0 6.52035,3.63758 19.86991,23.13901c3.84358,5.62477 8.13328,12.3947 12.21708,19.16463z" id="路径" fill="url(#linearGradient-2)" stroke="null" stroke-width="0"/>
      <path d="m124.13847,84.73367c0.85794,3.06499 1.33839,6.29839 1.33839,9.63284c0,19.80456 -16.36951,35.83683 -36.51396,35.83683c-20.14445,0 -36.51396,-16.03227 -36.51396,-35.83683c0,-18.69309 14.55067,-34.01805 33.15083,-35.70212c1.09817,-0.10104 2.23065,-0.13472 3.36313,-0.13472c16.747,0 30.88587,11.08113 35.17557,26.204c0,0 0,0 0,0z" id="路径" fill="#FFFFFF" stroke="null" stroke-width="0"/>
      <path d="m44.72747,113.09327c0,0 3.94653,41.02375 51.20191,49.17461c-15.47726,0 -31.332,0 -40.63208,0c-10.39824,0 -20.69353,-2.35769 -29.85634,-7.20779c-12.21708,-6.50048 -24.64006,-18.38995 -22.3751,-39.44073c1.64725,-15.32496 20.79649,-49.24196 20.79649,-49.24196l0.99521,-1.71775c0,0 0,0 0,-0.03368c7.61851,-9.59915 17.98244,-15.12287 26.83639,-18.32259c12.56025,-4.54697 26.39026,-4.88378 38.91619,-0.20209c27.48843,10.30646 33.15084,36.74623 33.52833,38.63238c-4.2897,-15.08919 -18.42857,-26.17032 -35.17557,-26.17032c-1.13249,0 -2.26497,0.06736 -3.36313,0.13472l0,-0.13472c0,-0.03368 -44.13248,1.78511 -40.8723,54.52992z" id="路径" fill="url(#linearGradient-3)" stroke="null" stroke-width="0"/>
     </g>
    </g>
   </g>
  </g>
 </g>
</svg>