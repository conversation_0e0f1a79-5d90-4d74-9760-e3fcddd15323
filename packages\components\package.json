{"name": "@admin-pkg/components", "version": "0.0.5", "description": "", "type": "module", "module": "./dist/index.es.js", "main": "../dist/index.es.js", "types": "dist/types/index.es.d.ts", "jsdelivr": "./dist/index.es.js", "unpkg": "./dist/index.es.js", "files": ["dist"], "exports": {".": {"types": "./dist/types/index.es.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.umd.js"}, "./*": "./*"}, "scripts": {"dev": "vite", "build": "vite build && npm run build:dts", "build:dts": "tsc -p tsconfig.dts.json || true"}, "keywords": ["ant-design-vue", "procomponents", "vue"], "author": {"name": "b<PERSON>qi<PERSON>", "email": "<EMAIL>", "url": "https://github.com/buqiyuan"}, "repository": {"type": "git", "url": "https://github.com/buqiyuan/vue3-antdv-admin/tree/main/packages/components", "directory": "packages/components"}, "homepage": "https://github.com/buqiyuan/vue3-antdv-admin/tree/main/packages/components#readme", "license": "MIT", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "devDependencies": {"typescript": "~5.5.4"}, "peerDependencies": {"vue": "^3.4.0"}}