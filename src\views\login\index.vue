<script setup lang="ts">
import { message, Modal } from 'ant-design-vue'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Icon } from '@/components/basic/icon'
import { useUserStore } from '@/store/modules/user'
import { to } from '@/utils/awaitTo'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const loginFormModel = ref({
  username: 'admin',
  password: 'a123456',
})

const handleSubmit = async () => {
  const { username, password } = loginFormModel.value
  if (username.trim() === '' || password.trim() === '') {
    return message.warning('用户名或密码不能为空！')
  }

  message.loading('登录中...', 0)
  loading.value = true
  console.log(loginFormModel.value)

  // 为了兼容后端接口，添加空的验证码字段
  const loginParams = {
    ...loginFormModel.value,
    verifyCode: '',
    captchaId: '',
  }

  const [err] = await to(userStore.login(loginParams))
  if (err) {
    Modal.error({
      title: () => '提示',
      content: () => err.message,
    })
  }
  else {
    message.success('登录成功！')
    setTimeout(() => router.replace((route.query.redirect as string) || '/'))
  }
  loading.value = false
  message.destroy()
}
</script>

<template>
  <div class="login-box">
    <div class="login-logo">
      <!-- <svg-icon name="logo" :size="45" /> -->
      <img src="~@/assets/images/logo.png" width="45">
      <h1 class="mb-0 ml-2 text-3xl font-bold">
        Antdv Admin
      </h1>
    </div>
    <a-form layout="horizontal" :model="loginFormModel" @submit.prevent="handleSubmit">
      <a-form-item>
        <a-input v-model:value="loginFormModel.username" size="large" placeholder="admin">
          <template #prefix>
            <Icon icon="ant-design:user-outlined" />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item>
        <a-input
          v-model:value="loginFormModel.password"
          size="large"
          type="password"
          placeholder="a123456"
          autocomplete="new-password"
        >
          <template #prefix>
            <Icon icon="ant-design:lock-outlined" />
          </template>
        </a-input>
      </a-form-item>

      <a-form-item>
        <a-button type="primary" html-type="submit" size="large" :loading="loading" block>
          登录
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<style lang="less" scoped>
  .login-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  height: 100vh;
  padding-top: 240px;
  background: url('@/assets/login.svg');
  background-size: 100%;

  .login-logo {
    display: flex;
    align-items: center;
    margin-bottom: 30px;

    .svg-icon {
      font-size: 48px;
    }
  }

  :deep(.ant-form) {
    width: 400px;

    .ant-col {
      width: 100%;
    }

    .ant-form-item-label {
      padding-right: 6px;
    }
  }
}
</style>
