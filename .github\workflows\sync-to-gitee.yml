name: Sync To Gitee
env:
  # 7 GiB by default on GitHub, setting to 6 GiB
  # https://docs.github.com/en/actions/using-github-hosted-runners/about-github-hosted-runners#supported-runners-and-hardware-resources
  NODE_OPTIONS: --max-old-space-size=6144
on:
  push:
    branches: [main]

# Ensures that only one mirror task will run at a time.
concurrency:
  group: repo-sync
  cancel-in-progress: true

jobs:
  repo-sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      # 设置服务器时区为东八区
      - name: Set time zone
        run: sudo timedatectl set-timezone 'Asia/Shanghai'

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          registry-url: https://registry.npmjs.com/

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: Cache ~/.pnpm-store
        uses: actions/cache@v3
        env:
          cache-name: cache-pnpm-store
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-preview-${{ env.cache-name }}-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-preview-${{ env.cache-name }}-
            ${{ runner.os }}-preview-
            ${{ runner.os }}-

      - name: Install dependencies
        run: pnpm i --frozen-lockfile

      - name: Build
        run: pnpm build
        env:
          FORCE_COLOR: 2

      - name: Deploy
        uses: peaceiris/actions-gh-pages@v3
        with:
          publish_dir: ./dist
          personal_token: ${{ secrets.PERSONAL_TOKEN }}
          commit_message: Update ghPages
          force_orphan: true

      - name: Mirror the Github organization repos to Gitee.
        uses: Yikun/hub-mirror-action@master
        with:
          src: 'github/buqiyuan'
          dst: 'gitee/buqiyuan'
          dst_key: ${{ secrets.GITEE_PRIVATE_KEY }}
          dst_token: ${{ secrets.GITEE_TOKEN }}
          static_list: 'vue3-antdv-admin'
          force_update: true
          debug: true

      - name: Build Gitee Pages
        uses: yanglbme/gitee-pages-action@main
        with:
          # 注意替换为你的 Gitee 用户名
          gitee-username: buqiyuan
          # 注意在 Settings->Secrets 配置 GITEE_PASSWORD
          gitee-password: ${{ secrets.GITEE_PASSWORD }}
          # 注意替换为你的 Gitee 仓库，仓库名严格区分大小写，请准确填写，否则会出错
          gitee-repo: buqiyuan/vue3-antdv-admin
          # 是否强制使用 HTTPS
          https: true
          # 要部署的分支，默认是 master，若是其他分支，则需要指定（指定的分支必须存在）
          branch: gh-pages
