// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as auth from './auth';
import * as account from './account';
import * as captcha from './captcha';
import * as authEmail from './authEmail';
import * as systemUser from './systemUser';
import * as systemRole from './systemRole';
import * as systemMenu from './systemMenu';
import * as systemParamConfig from './systemParamConfig';
import * as systemLog from './systemLog';
import * as systemDept from './systemDept';
import * as systemDictType from './systemDictType';
import * as systemDictItem from './systemDictItem';
import * as systemTask from './systemTask';
import * as systemOnline from './systemOnline';
import * as systemSse from './systemSse';
import * as systemServe from './systemServe';
import * as toolsStorage from './toolsStorage';
import * as systemEmail from './systemEmail';
import * as toolsUpload from './toolsUpload';
import * as health from './health';
import * as netDiskManage from './netDiskManage';
import * as netDiskOverview from './netDiskOverview';
import * as businessTodo from './businessTodo';
export default {
  auth,
  account,
  captcha,
  authEmail,
  systemUser,
  systemRole,
  systemMenu,
  systemParamConfig,
  systemLog,
  systemDept,
  systemDictType,
  systemDictItem,
  systemTask,
  systemOnline,
  systemSse,
  systemServe,
  toolsStorage,
  systemEmail,
  toolsUpload,
  health,
  netDiskManage,
  netDiskOverview,
  businessTodo,
};
